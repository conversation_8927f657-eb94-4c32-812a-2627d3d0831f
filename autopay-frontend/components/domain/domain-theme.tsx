'use client'

import { useDomain } from '@/lib/hooks/use-domain'
import { applyThemeColors, removeCustomStyles } from '@/lib/utils/cssOverride'
import { JSX, useEffect } from 'react'

/**
 * Component to apply domain-specific theme styles
 * This component should be placed high in the component tree
 * Note: This is now redundant with DomainContext but kept for backward compatibility
 */
export function DomainTheme(): JSX.Element | null {
  const { config } = useDomain()

  useEffect(() => {
    if (config?.theme?.name) {
      // Apply theme using the utility function
      applyThemeColors(config.theme.name)
    }

    // Cleanup when no config
    return () => {
      if (!config) {
        removeCustomStyles()
      }
    }
  }, [config])

  return null
}
