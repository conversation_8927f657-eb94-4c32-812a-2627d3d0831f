'use client'

import { useDomainConfig } from '@/lib/hooks/useDomainConfig'
import { useEffect } from 'react'

/**
 * Component to apply theme class to body element based on domain configuration
 */
export function ThemeApplier() {
  const { config } = useDomainConfig()

  useEffect(() => {
    // Get theme name from domain config
    const themeName = config?.theme?.name || 'blue' // default theme

    // Remove all existing theme classes
    const themeClasses = ['theme-red', 'theme-rose', 'theme-orange', 'theme-green', 'theme-blue', 'theme-yellow', 'theme-violet', 'theme-scaled']
    themeClasses.forEach(className => {
      document.body.classList.remove(className)
    })

    // Add the current theme class
    document.body.classList.add(`theme-${themeName}`)

    // Cleanup function to remove theme class when component unmounts
    return () => {
      document.body.classList.remove(`theme-${themeName}`)
    }
  }, [config?.theme?.name])

  return null // This component doesn't render anything
}
