'use client'

import React, { createContext, useEffect } from 'react'
import { useMetaTags } from 'react-metatags-hook'
import type { DomainConfig, DomainContextType } from '../types/domain'
import type { ThemeColors } from '../utils/cssOverride'
import { applyCustomCSS, applyThemeColors, removeCustomStyles } from '../utils/cssOverride'
import { resetApiUrl, setGlobalApiUrl } from '../utils/fetchHelper'

export const DomainContext = createContext<DomainContextType | null>(null)

interface DomainProviderProps {
  children: React.ReactNode
  initialConfig?: DomainConfig | null
}

export function DomainProvider({ children, initialConfig }: DomainProviderProps): React.JSX.Element {
  // Use initialConfig directly since we only rely on SSR now
  const config = initialConfig || null

  // Use meta tags hook for SEO
  useMetaTags(
    {
      title: config?.seo?.title || '',
      description: config?.seo?.description || '',
      metas: [...(config?.seo?.keywords ? [{ name: 'keywords', content: config.seo.keywords }] : [])],
      links: [...(config?.branding?.favicon_url ? [{ rel: 'icon', href: config.branding.favicon_url }] : [])],
      openGraph: {
        ...(config?.seo?.og_image ? { image: config.seo.og_image } : {}),
        ...(config?.seo?.title ? { title: config.seo.title } : {}),
        ...(config?.seo?.description ? { description: config.seo.description } : {}),
      },
    },
    [config?.seo, config?.branding?.favicon_url]
  )

  // Update global API URL when config changes
  useEffect(() => {
    if (config?.backend_hostname) {
      setGlobalApiUrl(`https://${config.backend_hostname}`)
    } else {
      resetApiUrl()
    }
  }, [config?.backend_hostname])

  // Apply custom CSS when config changes (theme is handled by ThemeApplier)
  useEffect(() => {
    if (config?.custom_css && Object.keys(config.custom_css).length > 0) {
      // Apply custom CSS using utility function
      applyCustomCSS(config.custom_css)
    }

    // Cleanup function to remove custom styles when config changes
    return () => {
      if (!config?.custom_css) {
        removeCustomStyles()
      }
    }
  }, [config?.custom_css])

  const contextValue: DomainContextType = {
    config,
  }

  return <DomainContext.Provider value={contextValue}>{children}</DomainContext.Provider>
}
